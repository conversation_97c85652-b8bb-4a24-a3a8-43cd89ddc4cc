# FilterModel 接口文档

基于 `FilterModel.js` 和 `FilterConfig.js` 的完整枚举值和接口定义。

## FilterModel 数据结构

```typescript
interface FilterModel {
  action: string                    // 事件行为类型
  eventInfo: EventInfo             // 事件信息
  eventAggregateProperty: EventAggregateProperty  // 事件聚合属性
  eventFilterProperty: EventFilterProperty        // 事件过滤属性
  key: number                      // 随机Key
  firstAction: string               // 第一个事件类型
  firstTimeValue: number           // 第一个时间值 (默认: 1)
  firstTimeUnit: string           // 第一个时间单位 (默认: "HOUR")
  lastTimeValue: number           // 最后时间值 (默认: 1)
  lastTimeUnit: string            // 最后时间单位 (默认: "HOUR")
  lastAction?: string             // 最后事件行为
  lastEventInfo?: EventInfo       // 最后事件信息
  lastEventFilterProperty?: EventFilterProperty  // 最后事件过滤属性
  todayDoEvents: TodayDoEvent[]   // 依次做过事件列表 (最多5个)
}

interface EventInfo {
  id: string                      // 事件ID
  name: string                    // 事件名称
}

interface EventAggregateProperty {
  propertyType: string            // 属性类型: "EVENT_PROPERTY" | "TIMES"
  fun?: string                    // 聚合函数
  operator: string                // 操作符
  value: any                      // 属性值
  property?: {
    fieldType: string             // 字段类型
    field: string                 // 字段名
  }
}

interface EventFilterProperty {
  connector: string               // 连接符: "AND" | "OR"
  filters: Filter[]               // 过滤条件数组
}

interface TodayDoEvent {
  eventInfo: EventInfo            // 事件信息
  eventFilterProperty: EventFilterProperty  // 事件过滤属性
}
```

## 枚举值定义

### 1. action (事件行为类型)
```javascript
const ACTION_TYPES = {
  "DONE": "做过",           // 默认：做过
  "NOT_DO": "未做过",       // 未做过
  "DO_SEQ": "依次做过",     // 依次做过（用于 DO_SEQ 模式）
  "FIRST_DO": "先做过"      // 先做过
}
```

### 2. firstAction (第一个事件类型)
```javascript
const FIRST_EVENT_TYPES = {
  "DONE": "做过",                    // 默认：当天做过
  "FIRST_DO_LAST_NOT_DO": "先做过, 后未做过",  // 先做过，后未做过
  "DO_SEQ": "依次做过"              // 当天依次做过
}
```

### 3. firstTimeUnit / lastTimeUnit (时间单位)
```javascript
const TIME_UNITS = {
  "HOUR": "小时",     // 默认：小时
  "MINUTE": "分钟"    // 分钟
}
```

### 4. operator (操作符类型)
```javascript
const OPERATORS = {
  "EQ": "等于",
  "NE": "不等于", 
  "GT": "大于",
  "GTE": "大于等于",
  "LT": "小于",
  "LTE": "小于等于",
  "BETWEEN": "范围",
  "ADVANCED_BETWEEN": "高级范围",
  "IN": "包含",
  "NOT_IN": "不包含",
  "IS_NOT_NULL": "有值",
  "IS_NULL": "空值",
  "ALL": "全部",
  "LIKE": "匹配",
  "NOT_LIKE": "不匹配",
  "START_WITH": "开头匹配",
  "NOT_START_WITH": "开头不匹配",
  "END_WITH": "结尾匹配",
  "NOT_END_WITH": "结尾不匹配",
  "IS_TRUE": "是",
  "IS_FALSE": "否"
}
```

### 5. propertyType (属性类型)
```javascript
const PROPERTY_TYPES = {
  "EVENT_PROPERTY": "事件属性",  // 需要选择具体事件字段
  "TIMES": "次数"              // 事件次数统计
}
```

### 6. fieldType (字段数据类型)
```javascript
const FIELD_TYPES = {
  "STRING": "字符串",
  "INT": "整数",
  "LONG": "长整数", 
  "DOUBLE": "浮点数",
  "BOOL": "布尔值",
  "DATE": "日期",
  "DATETIME": "日期时间",
  "TIMESTAMP": "时间戳",
  "HIVE_DATE": "Hive日期",
  "HIVE_TIMESTAMP": "Hive时间戳",
  "LABEL": "标签"
}
```

### 7. fun (聚合函数)

#### 数字类型 (INT, LONG)
```javascript
const NUMBER_FUNCTIONS = [
  { name: "去重数", value: "UNIQUE_COUNT" },
  { name: "计数", value: "COUNT" },
  { name: "总和", value: "SUM" },
  { name: "平均值", value: "AVG" },
  { name: "最大值", value: "MAX" },
  { name: "最小值", value: "MIN" }
]
```

#### 浮点数类型 (DOUBLE)
```javascript
const DOUBLE_FUNCTIONS = [
  { name: "计数", value: "COUNT" },
  { name: "总和", value: "SUM" },
  { name: "平均值", value: "AVG" },
  { name: "最大值", value: "MAX" },
  { name: "最小值", value: "MIN" }
]
```

#### 字符串类型 (STRING)
```javascript
const STRING_FUNCTIONS = [
  { name: "去重数", value: "UNIQUE_COUNT" }
]
```

#### 布尔类型 (BOOL)
```javascript
const BOOL_FUNCTIONS = [
  { name: "去重数", value: "UNIQUE_COUNT" }
]
```

#### 时间类型 (TIMESTAMP)
```javascript
const TIMESTAMP_FUNCTIONS = [
  { name: "计数", value: "COUNT" }
]
```

#### 次数类型 (TIMES)
```javascript
const TIMES_FUNCTIONS = [
  { name: "去重数", value: "UNIQUE_COUNT" },
  { name: "总和", value: "SUM" },
  { name: "平均值", value: "AVG" },
  { name: "最大值", value: "MAX" },
  { name: "最小值", value: "MIN" }
]
```

### 8. connector (连接符)
```javascript
const CONNECTORS = {
  "AND": "且",
  "OR": "或"
}
```

### 9. 各数据类型支持的操作符

#### 数字类型 (INT, LONG, DOUBLE)
```javascript
const NUMBER_OPERATORS = [
  "EQ", "NE", "IS_NOT_NULL", "IS_NULL", 
  "GT", "GTE", "LT", "LTE", "BETWEEN"
]
```

#### 布尔类型 (BOOL)
```javascript
const BOOL_OPERATORS = ["IS_TRUE", "IS_FALSE"]
```

#### 标签类型 (LABEL)
```javascript
const LABEL_OPERATORS = ["IN", "NOT_IN", "ALL"]
```

## 接口示例

### 基础事件过滤 (DONE)
```json
{
  "action": "DONE",
  "eventInfo": {
    "id": "event_001",
    "name": "用户登录"
  },
  "eventAggregateProperty": {
    "propertyType": "TIMES",
    "fun": "COUNT",
    "operator": "GTE", 
    "value": 1
  },
  "eventFilterProperty": {
    "connector": "AND",
    "filters": []
  },
  "key": 0.27782986122314335,
  "firstAction": "DONE",
  "firstTimeValue": 24,
  "firstTimeUnit": "HOUR",
  "lastTimeValue": 1,
  "lastTimeUnit": "HOUR",
  "lastAction": undefined,
  "lastEventInfo": undefined,
  "lastEventFilterProperty": undefined,
  "todayDoEvents": []
}
```

### 先做过后未做过 (FIRST_DO_LAST_NOT_DO)
```json
{
  "action": "FIRST_DO",
  "eventInfo": {
    "id": "event_001", 
    "name": "用户注册"
  },
  "eventAggregateProperty": {
    "propertyType": "TIMES",
    "operator": "GTE",
    "value": 1
  },
  "eventFilterProperty": {
    "connector": "AND",
    "filters": []
  },
  "key": 0.123456789,
  "firstAction": "FIRST_DO_LAST_NOT_DO",
  "firstTimeValue": 1,
  "firstTimeUnit": "HOUR",
  "lastTimeValue": 7,
  "lastTimeUnit": "HOUR",
  "lastAction": "NOT_DO",
  "lastEventInfo": {
    "id": "event_002",
    "name": "用户购买"
  },
  "lastEventFilterProperty": {
    "connector": "AND",
    "filters": []
  },
  "todayDoEvents": []
}
```

### 依次做过 (DO_SEQ)
```json
{
  "action": "DO_SEQ",
  "eventInfo": {},
  "eventAggregateProperty": {},
  "eventFilterProperty": {},
  "key": 0.987654321,
  "firstAction": "DO_SEQ", 
  "firstTimeValue": 24,
  "firstTimeUnit": "HOUR",
  "lastTimeValue": 1,
  "lastTimeUnit": "HOUR",
  "lastAction": undefined,
  "lastEventInfo": undefined,
  "lastEventFilterProperty": undefined,
  "todayDoEvents": [
    {
      "eventInfo": {
        "id": "event_001",
        "name": "用户登录"
      },
      "eventFilterProperty": {
        "connector": "AND",
        "filters": []
      }
    },
    {
      "eventInfo": {
        "id": "event_002", 
        "name": "浏览商品"
      },
      "eventFilterProperty": {
        "connector": "AND",
        "filters": []
      }
    }
  ]
}
```

### 事件属性过滤示例
```json
{
  "action": "DONE",
  "eventInfo": {
    "id": "event_purchase",
    "name": "商品购买"
  },
  "eventAggregateProperty": {
    "propertyType": "EVENT_PROPERTY",
    "fun": "SUM",
    "operator": "GT",
    "value": 1000,
    "property": {
      "fieldType": "DOUBLE",
      "field": "order_amount"
    }
  },
  "eventFilterProperty": {
    "connector": "AND",
    "filters": [
      {
        "property": {
          "fieldType": "STRING",
          "field": "product_category"
        },
        "operator": "EQ",
        "value": "electronics"
      }
    ]
  },
  "firstAction": "DONE",
  "firstTimeValue": 7,
  "firstTimeUnit": "HOUR"
}
```

## 验证规则

### 通用验证
- `action`: 必填
- `firstTimeValue`: 必须 > 0
- `firstTimeUnit`: 必填

### DONE 模式验证
- `eventInfo.id`: 必填
- `eventAggregateProperty.propertyType`: 必填
- 如果 `propertyType` 为 `EVENT_PROPERTY`:
  - 需要 `fun` (聚合函数)
  - 需要 `property.field` (字段名)
- 需要 `operator` (操作符)
- 对于非空值检查操作符，需要 `value`

### FIRST_DO_LAST_NOT_DO 模式验证  
- `eventInfo.id`: 必填 (第一个事件)
- `lastEventInfo.id`: 必填 (最后一个事件)
- `lastAction`: 必须为 `"NOT_DO"`
- `lastTimeValue`: 必须 > 0

### DO_SEQ 模式验证
- `action`: 必须为 `"DO_SEQ"` 
- `todayDoEvents`: 不能为空
- 每个事件需要有效的 `eventInfo.id`
- 最多支持 5 个事件

### 数据类型验证规则

#### STRING (字符串)
```javascript
{
  required: true,
  maxLen: 500,
  message: {
    required: "请输入",
    maxLen: "最大输入500个字符"
  }
}
```

#### INT (整数)
```javascript
{
  required: true,
  maxLen: 11,
  regex: "^[+-]?\\d*$",
  message: {
    required: "请输入",
    maxLen: "最大长度11个字符",
    regex: "请输入数字"
  }
}
```

#### LONG (长整数)
```javascript
{
  required: true,
  maxLen: 20,
  regex: "^[+-]?\\d*$",
  message: {
    required: "请输入",
    maxLen: "最大长度20个字符",
    regex: "请输入数字"
  }
}
```

#### DOUBLE (浮点数)
```javascript
{
  required: true,
  maxLen: 20,
  regex: "^[+-]?\\d+(\\.\\d+)?$",
  message: {
    required: "请输入",
    maxLen: "最大长度20个字符",
    regex: "请输入浮点数字"
  }
}
```

#### DATE/DATETIME/TIMESTAMP
```javascript
{
  required: true,
  message: {
    required: "请输入日期时间"
  }
}
```

#### BOOL (布尔值)
```javascript
{
  required: false,
  message: {
    required: "请输入"
  }
}
```

## 常量限制

```javascript
const LIMITS = {
  MAX_TODAY_DO_EVENTS: 5,        // 依次做过最大事件数
  MAX_FILTER_COUNT: 20,          // 最大过滤条件数
  MAX_STRING_LENGTH: 500,        // 字符串最大长度
  MAX_INT_LENGTH: 11,            // 整数最大长度
  MAX_LONG_LENGTH: 20            // 长整数最大长度
}
```

## 日期格式配置

```javascript
const DATE_FORMATTERS = {
  "DATE": "YYYY-MM-DD",
  "DATETIME": "YYYY-MM-DD HH:mm:ss",
  "TIMESTAMP": "YYYY-MM-DD HH:mm:ss",
  "HIVE_DATE": "YYYY-MM-DD",
  "HIVE_TIMESTAMP": "YYYY-MM-DD HH:mm:ss"
}
```

## 相对时间配置

```javascript
const RELATIVE_TIME = {
  "0": "今天",
  "1": "昨天", 
  "2": "前天"
}
```

## 方法说明

### 实例方法

- `valid()`: 验证 FilterModel 数据有效性
- `toJson()`: 转换为 JSON 对象
- `changeProperty(property)`: 更新属性
- `addTodayDoEvent()`: 添加依次做过事件
- `removeTodayDoEvent(index)`: 删除依次做过事件
- `updateTodayDoEvent(index, eventData)`: 更新依次做过事件
- `shouldShowTimeSettings()`: 是否显示时间设置
- `shouldShowLastEventSettings()`: 是否显示最后事件设置
- `shouldShowTodayDoEventsList()`: 是否显示依次做过事件列表
- `getValidationErrors()`: 获取验证错误信息
- `resetToDefault()`: 重置到默认状态

### 静态方法

- `FilterModel.fromJson(data)`: 从 JSON 创建实例

## 使用注意事项

1. **firstAction 决定显示模式**:
   - `DONE`: 显示基础事件过滤
   - `FIRST_DO_LAST_NOT_DO`: 显示先做过后未做过配置
   - `DO_SEQ`: 显示依次做过事件列表

2. **action 与 firstAction 的关系**:
   - `DO_SEQ` 时，action 必须为 `DO_SEQ`
   - `FIRST_DO_LAST_NOT_DO` 时，lastAction 必须为 `NOT_DO`

## Filter 组件参数

### 组件 Props

Filter.vue 组件支持以下参数：

```typescript
interface FilterProps {
  value: Object                    // 过滤器数据对象
  dataProvider: Object            // 数据提供者对象 (必需)
  onChange: Function              // 数据变更回调函数 (必需)
  mode: String                    // 显示模式: 'edit' | 'detail' (默认: 'edit')
  showInitLine: Boolean           // 是否显示初始行 (默认: true)
  isActionCollection: Boolean     // 是否为行为集合模式 (默认: false)
  externalFirstAction: String     // 外部传入的firstAction (默认: null)
  showPushData: Boolean          // 是否显示推送数据选项 (默认: false)
}
```

### 参数详细说明

#### externalFirstAction
- **类型**: `String`
- **默认值**: `null`
- **说明**: 外部传入的firstAction，用于组件联动场景
- **使用场景**: 当有多个Filter组件需要联动时，第二个组件可以使用第一个组件的firstAction作为默认值
- **优先级**: 高于内部获取的firstAction

**示例**:
```vue
<!-- 第一个Filter组件 -->
<Filter
  :value="firstFilterValue"
  :dataProvider="dataProvider"
  :onChange="onFirstFilterChange"
/>

<!-- 第二个Filter组件，使用第一个组件的firstAction -->
<Filter
  :value="secondFilterValue"
  :dataProvider="dataProvider"
  :onChange="onSecondFilterChange"
  :externalFirstAction="getFirstFilterFirstAction(firstFilterValue)"
/>
```

#### showPushData
- **类型**: `Boolean`
- **默认值**: `false`
- **说明**: 控制"推送数据"选项的显示与隐藏
- **影响范围**: 所有FilterSingle子组件中的推送数据radio-group
- **使用场景**: 根据业务需求决定是否需要推送数据功能

**示例**:
```vue
<!-- 显示推送数据选项 -->
<Filter
  :value="filterValue"
  :dataProvider="dataProvider"
  :onChange="onFilterChange"
  :showPushData="true"
/>

<!-- 隐藏推送数据选项 -->
<Filter
  :value="filterValue"
  :dataProvider="dataProvider"
  :onChange="onFilterChange"
  :showPushData="false"
/>
```

### 组件联动示例

```vue
<template>
  <div>
    <!-- 主Filter组件 -->
    <Filter
      :value="mainFilterValue"
      :dataProvider="dataProvider"
      :onChange="handleMainFilterChange"
      :showPushData="true"
    />

    <!-- 联动Filter组件 -->
    <Filter
      :value="linkedFilterValue"
      :dataProvider="dataProvider"
      :onChange="handleLinkedFilterChange"
      :externalFirstAction="getMainFilterFirstAction()"
      :showPushData="false"
    />
  </div>
</template>

<script>
import FilterModelUtil from './models/FilterModelUtil'

export default {
  data() {
    return {
      mainFilterValue: {},
      linkedFilterValue: {},
    }
  },
  methods: {
    handleMainFilterChange(validJson, fullValue) {
      this.mainFilterValue = fullValue || validJson || {}
    },

    handleLinkedFilterChange(validJson, fullValue) {
      this.linkedFilterValue = fullValue || validJson || {}
    },

    getMainFilterFirstAction() {
      return FilterModelUtil.getFirstFilterFirstAction(this.mainFilterValue)
    }
  }
}
</script>
```

### 注意事项

1. **externalFirstAction 优先级**: 当传入 `externalFirstAction` 时，组件内部的firstAction选择框会被禁用
2. **showPushData 影响范围**: 该参数会影响所有子组件中的推送数据显示，包括：
   - FilterSingleDone 组件
   - FilterSingleFirstDoLastNotDo 组件
   - FilterSingleDoSeq 组件（主过滤器和依次做过事件）
3. **数据完整性**: 使用 `externalFirstAction` 时，建议保存完整的数据对象（包括未校验通过的），以确保能正确获取firstAction

3. **数据类型与操作符匹配**:
   - 不同 fieldType 支持不同的 operator
   - 聚合函数 fun 也与 fieldType 相关

4. **依次做过事件限制**:
   - 最多支持 5 个事件
   - 每个事件必须有有效的 eventInfo.id 